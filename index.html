<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI公众号文章生成器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/openai@4.20.1/dist/index.min.js"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div id="app" class="container mx-auto px-4 py-8">
        <!-- 配置面板 -->
        <div id="config-panel" class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-bold mb-4">配置设置</h2>
            <!-- 配置表单内容 -->
        </div>

        <!-- 对话界面 -->
        <div id="chat-panel" class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-bold mb-4">AI对话</h2>
            <div id="chat-messages" class="h-96 overflow-y-auto border rounded p-4 mb-4">
                <!-- 对话消息 -->
            </div>
            <div class="flex gap-2">
                <input id="user-input" type="text" class="flex-1 border rounded px-3 py-2" placeholder="输入您的消息...">
                <button id="send-btn" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">发送</button>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex gap-4 justify-center">
            <button id="generate-article-btn" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 font-bold">
                一键成文
            </button>
            <button id="clear-config-btn" class="bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600">
                清除配置
            </button>
        </div>

        <!-- 生成的文章显示 -->
        <div id="article-panel" class="bg-white rounded-lg shadow-md p-6 mt-6 hidden">
            <h2 class="text-xl font-bold mb-4">生成的文章</h2>
            <div id="generated-article" class="prose max-w-none"></div>
        </div>
    </div>

    <script src="js/app.js"></script>
</body>
</html>