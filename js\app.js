class WeChatArticleGenerator {
    constructor() {
        this.config = this.loadConfig();
        this.chatHistory = [];
        this.openai = null;
        this.initializeApp();
    }

    loadConfig() {
        const saved = localStorage.getItem('wechat-generator-config');
        return saved ? JSON.parse(saved) : {
            apiUrl: '',
            apiKey: '',
            customPrompt: '',
            referenceArticles: []
        };
    }

    saveConfig() {
        localStorage.setItem('wechat-generator-config', JSON.stringify(this.config));
    }

    initializeApp() {
        this.renderConfigPanel();
        this.bindEvents();
        if (this.config.apiKey && this.config.apiUrl) {
            this.initializeOpenAI();
        }
    }

    renderConfigPanel() {
        const configPanel = document.getElementById('config-panel');
        configPanel.innerHTML = `
            <h2 class="text-xl font-bold mb-4">配置设置</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">API URL</label>
                    <input id="api-url" type="text" value="${this.config.apiUrl}" 
                           class="w-full border rounded px-3 py-2" placeholder="https://api.openai.com/v1">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">API Key</label>
                    <input id="api-key" type="password" value="${this.config.apiKey}" 
                           class="w-full border rounded px-3 py-2" placeholder="sk-...">
                </div>
            </div>
            <div class="mt-4">
                <label class="block text-sm font-medium mb-2">自定义成文提示词</label>
                <textarea id="custom-prompt" rows="3" class="w-full border rounded px-3 py-2" 
                          placeholder="请将对话内容整理成一篇微信公众号文章...">${this.config.customPrompt}</textarea>
            </div>
            <div class="mt-4">
                <label class="block text-sm font-medium mb-2">参考文章（用于学习写作风格）</label>
                <textarea id="reference-articles" rows="5" class="w-full border rounded px-3 py-2" 
                          placeholder="粘贴您的参考文章内容...">${this.config.referenceArticles.join('\n\n---\n\n')}</textarea>
            </div>
            <button id="save-config-btn" class="mt-4 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                保存配置
            </button>
        `;
    }

    bindEvents() {
        // 保存配置
        document.addEventListener('click', (e) => {
            if (e.target.id === 'save-config-btn') {
                this.saveConfiguration();
            }
            if (e.target.id === 'send-btn') {
                this.sendMessage();
            }
            if (e.target.id === 'generate-article-btn') {
                this.generateArticle();
            }
            if (e.target.id === 'clear-config-btn') {
                this.clearConfiguration();
            }
        });

        // 回车发送消息
        document.getElementById('user-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });
    }

    saveConfiguration() {
        this.config.apiUrl = document.getElementById('api-url').value;
        this.config.apiKey = document.getElementById('api-key').value;
        this.config.customPrompt = document.getElementById('custom-prompt').value;
        this.config.referenceArticles = document.getElementById('reference-articles').value
            .split('\n\n---\n\n').filter(article => article.trim());
        
        this.saveConfig();
        this.initializeOpenAI();
        alert('配置已保存！');
    }

    initializeOpenAI() {
        if (!this.config.apiKey || !this.config.apiUrl) return;
        
        this.openai = new OpenAI({
            apiKey: this.config.apiKey,
            baseURL: this.config.apiUrl,
            dangerouslyAllowBrowser: true
        });
    }

    async sendMessage() {
        const input = document.getElementById('user-input');
        const message = input.value.trim();
        if (!message || !this.openai) return;

        this.addMessageToChat('user', message);
        input.value = '';

        try {
            const response = await this.openai.chat.completions.create({
                model: 'gpt-3.5-turbo',
                messages: [
                    ...this.chatHistory,
                    { role: 'user', content: message }
                ]
            });

            const aiResponse = response.choices[0].message.content;
            this.addMessageToChat('assistant', aiResponse);
        } catch (error) {
            this.addMessageToChat('system', `错误: ${error.message}`);
        }
    }

    addMessageToChat(role, content) {
        this.chatHistory.push({ role, content });
        
        const chatMessages = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `mb-4 ${role === 'user' ? 'text-right' : 'text-left'}`;
        
        const roleClass = role === 'user' ? 'bg-blue-500 text-white' : 
                         role === 'assistant' ? 'bg-gray-200' : 'bg-red-100';
        
        messageDiv.innerHTML = `
            <div class="inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${roleClass}">
                ${content}
            </div>
        `;
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    async generateArticle() {
        if (!this.openai || this.chatHistory.length === 0) {
            alert('请先配置API并进行对话');
            return;
        }

        const chatContent = this.chatHistory
            .filter(msg => msg.role !== 'system')
            .map(msg => `${msg.role}: ${msg.content}`)
            .join('\n\n');

        const prompt = this.buildArticlePrompt(chatContent);

        try {
            const response = await this.openai.chat.completions.create({
                model: 'gpt-3.5-turbo',
                messages: [{ role: 'user', content: prompt }]
            });

            const article = response.choices[0].message.content;
            this.displayGeneratedArticle(article);
        } catch (error) {
            alert(`生成文章失败: ${error.message}`);
        }
    }

    buildArticlePrompt(chatContent) {
        let prompt = this.config.customPrompt || 
            '请将以下对话内容整理成一篇结构清晰、适合微信公众号发布的文章：';
        
        if (this.config.referenceArticles.length > 0) {
            prompt += '\n\n参考文章风格：\n' + this.config.referenceArticles.join('\n\n---\n\n');
        }
        
        prompt += '\n\n对话内容：\n' + chatContent;
        
        return prompt;
    }

    displayGeneratedArticle(article) {
        const articlePanel = document.getElementById('article-panel');
        const articleContent = document.getElementById('generated-article');
        
        articleContent.innerHTML = article.replace(/\n/g, '<br>');
        articlePanel.classList.remove('hidden');
        
        articlePanel.scrollIntoView({ behavior: 'smooth' });
    }

    clearConfiguration() {
        if (confirm('确定要清除所有配置吗？此操作不可撤销。')) {
            localStorage.removeItem('wechat-generator-config');
            this.config = {
                apiUrl: '',
                apiKey: '',
                customPrompt: '',
                referenceArticles: []
            };
            this.chatHistory = [];
            this.openai = null;
            this.renderConfigPanel();
            document.getElementById('chat-messages').innerHTML = '';
            document.getElementById('article-panel').classList.add('hidden');
            alert('配置已清除！');
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new WeChatArticleGenerator();
});